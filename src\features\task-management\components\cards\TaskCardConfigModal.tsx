'use client';

import React, { useState } from 'react';

import { Eye, Grid3X3, Layout, Palette, RotateCcw, Settings, Sparkles, Type } from 'lucide-react';

import { Badge } from '@/shared/components/badge';
import { Button } from '@/shared/components/button';
import { Card, CardContent } from '@/shared/components/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/shared/components/dialog';
import { Label } from '@/shared/components/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/select';
import { Switch } from '@/shared/components/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/shared/components/tabs';
import type { Task, Vehicle } from '@/core/types';
import { defaultTaskCardConfig, FieldStyle, TaskCardConfig } from '@/core/types/taskCardConfig';

import { TaskRowHighlightProvider } from '@/shared/components/contexts/TaskRowHighlightContext';

import { ConfigurableTaskCard } from './ConfigurableTaskCard';
import { DraggableFieldConfigSection } from './DraggableFieldConfigSection';

// 字段名称中文映射
const fieldNameMap: Record<string, string> = {
  // 顶部区域字段
  projectName: '工程名称',
  constructionSite: '施工部位',
  strength: '强度',
  progressRing: '进度环',
  dispatchReminder: '调度提醒',
  messageIcon: '消息图标',

  // 车辆区域字段
  vehicleCards: '车辆卡片',

  // 内容区域字段
  requiredVolume: '需求量',
  completedVolume: '已供量',
  scheduledTime: '计划时间',
  contactPhone: '联系电话',
  completedProgress: '完成进度',
  estimatedDuration: '预计时长',
  constructionLocation: '施工位置',
  taskStatus: '任务状态',

  // 底部区域字段
  customerName: '客户名称',
  createdAt: '创建时间',
  taskNumber: '任务编号',
  updatedAt: '更新时间',
};

interface TaskCardConfigModalProps {
  open: boolean;
  onOpenChangeAction: (open: boolean) => void;
  config: TaskCardConfig;
  onConfigChangeAction: (config: TaskCardConfig) => void;
  onPreviewConfigChange?: (config: TaskCardConfig) => void; // 新增：实时预览配置变更回调
  previewTask?: Task;
  previewVehicles?: Vehicle[];
}

/**
 * 字段配置组件
 */
const FieldConfigSection: React.FC<{
  title: string;
  fields: Record<string, FieldStyle>;
  onFieldChange: (fieldKey: string, field: FieldStyle) => void;
  disabledFields?: string[];
}> = ({ title, fields, onFieldChange, disabledFields = [] }) => {
  return (
    <div className='space-y-4'>
      <h4 className='font-medium text-sm flex items-center gap-2'>
        <Type className='w-4 h-4' />
        {title}
      </h4>
      <div className='space-y-3'>
        {Object.entries(fields).map(([key, field]) => {
          const isDisabled = disabledFields.includes(key);
          return (
            <div key={key} className='grid grid-cols-6 gap-2 items-center p-2 border rounded-lg'>
              <div className='col-span-1'>
                <Label className='text-xs'>{fieldNameMap[key] || key}</Label>
              </div>

              {/* 显隐开关 */}
              <div className='col-span-1 flex justify-center'>
                <Switch
                  checked={field.visible}
                  onCheckedChange={visible => onFieldChange(key, { ...field, visible })}
                  disabled={isDisabled}
                />
              </div>

              {/* 字体大小 */}
              <div className='col-span-1'>
                <Select
                  value={field.fontSize}
                  onValueChange={(fontSize: any) => onFieldChange(key, { ...field, fontSize })}
                >
                  <SelectTrigger className='h-8 text-xs'>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='xs'>极小</SelectItem>
                    <SelectItem value='sm'>小</SelectItem>
                    <SelectItem value='base'>正常</SelectItem>
                    <SelectItem value='lg'>大</SelectItem>
                    <SelectItem value='xl'>极大</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* 字体粗细 */}
              <div className='col-span-1'>
                <Select
                  value={field.fontWeight}
                  onValueChange={(fontWeight: any) => onFieldChange(key, { ...field, fontWeight })}
                >
                  <SelectTrigger className='h-8 text-xs'>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='normal'>正常</SelectItem>
                    <SelectItem value='medium'>中等</SelectItem>
                    <SelectItem value='semibold'>半粗</SelectItem>
                    <SelectItem value='bold'>粗体</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* 颜色 */}
              <div className='col-span-1'>
                <Select
                  value={field.color}
                  onValueChange={(color: any) => onFieldChange(key, { ...field, color })}
                >
                  <SelectTrigger className='h-8 text-xs'>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='default'>默认</SelectItem>
                    <SelectItem value='muted'>柔和</SelectItem>
                    <SelectItem value='primary'>主色</SelectItem>
                    <SelectItem value='secondary'>次要</SelectItem>
                    <SelectItem value='destructive'>警告</SelectItem>
                    <SelectItem value='warning'>提醒</SelectItem>
                    <SelectItem value='success'>成功</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* 对齐方式 */}
              <div className='col-span-1'>
                <Select
                  value={field.textAlign}
                  onValueChange={(textAlign: any) => onFieldChange(key, { ...field, textAlign })}
                >
                  <SelectTrigger className='h-8 text-xs'>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='left'>左对齐</SelectItem>
                    <SelectItem value='center'>居中</SelectItem>
                    <SelectItem value='right'>右对齐</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

/**
 * 任务卡片配置模态框
 */
export const TaskCardConfigModal: React.FC<TaskCardConfigModalProps> = ({
  open,
  onOpenChangeAction,
  config,
  onConfigChangeAction,
  onPreviewConfigChange,
  previewTask,
  previewVehicles = [],
}) => {
  // 确保配置完整性，合并默认配置
  const mergeWithDefaults = (config: TaskCardConfig): TaskCardConfig => {
    return {
      style: {
        ...defaultTaskCardConfig.style,
        ...config.style,
      },
      areas: {
        top: {
          ...defaultTaskCardConfig.areas.top,
          ...config.areas?.top,
          fields: {
            ...defaultTaskCardConfig.areas.top.fields,
            ...config.areas?.top?.fields,
          },
        },
        vehicle: {
          ...defaultTaskCardConfig.areas.vehicle,
          ...config.areas?.vehicle,
          fields: {
            ...defaultTaskCardConfig.areas.vehicle.fields,
            ...config.areas?.vehicle?.fields,
          },
        },
        content: {
          ...defaultTaskCardConfig.areas.content,
          ...config.areas?.content,
          fields: {
            ...defaultTaskCardConfig.areas.content.fields,
            ...config.areas?.content?.fields,
          },
        },
        bottom: {
          ...defaultTaskCardConfig.areas.bottom,
          ...config.areas?.bottom,
          fields: {
            ...defaultTaskCardConfig.areas.bottom.fields,
            ...config.areas?.bottom?.fields,
          },
        },
      },
    };
  };

  const [previewConfig, setPreviewConfig] = useState<TaskCardConfig>(() =>
    mergeWithDefaults(config)
  );

  // 同步外部配置到预览配置
  React.useEffect(() => {
    setPreviewConfig(mergeWithDefaults(config));
  }, [config]);

  // 更新样式配置
  const updateStyleConfig = (updates: Partial<TaskCardConfig['style']>) => {
    const newConfig = {
      ...previewConfig,
      style: { ...previewConfig.style, ...updates },
    };
    setPreviewConfig(newConfig);
    // 实时触发外部配置变更，实现即时预览
    onPreviewConfigChange?.(newConfig);
  };

  // 更新区域配置
  const updateAreaConfig = (areaKey: keyof TaskCardConfig['areas'], updates: any) => {
    const newConfig = {
      ...previewConfig,
      areas: {
        ...previewConfig.areas,
        [areaKey]: { ...previewConfig.areas[areaKey], ...updates },
      },
    };
    setPreviewConfig(newConfig);
    // 实时触发外部配置变更，实现即时预览
    onPreviewConfigChange?.(newConfig);
  };

  // 更新字段配置
  const updateFieldConfig = (
    areaKey: keyof TaskCardConfig['areas'],
    fieldKey: string,
    field: FieldStyle
  ) => {
    const newConfig = {
      ...previewConfig,
      areas: {
        ...previewConfig.areas,
        [areaKey]: {
          ...previewConfig.areas[areaKey],
          fields: {
            ...previewConfig.areas[areaKey].fields,
            [fieldKey]: field,
          },
        },
      },
    };
    setPreviewConfig(newConfig);
    // 实时触发外部配置变更，实现即时预览
    onPreviewConfigChange?.(newConfig);
  };

  // 更新字段排序
  const updateFieldOrder = (areaKey: keyof TaskCardConfig['areas'], newOrder: string[]) => {
    const currentFields = previewConfig.areas[areaKey].fields;
    const updatedFields = { ...currentFields };

    // 根据新的排序更新order属性
    newOrder.forEach((fieldKey, index) => {
      if (updatedFields[fieldKey]) {
        updatedFields[fieldKey] = {
          ...updatedFields[fieldKey],
          order: index,
        };
      }
    });

    const newConfig = {
      ...previewConfig,
      areas: {
        ...previewConfig.areas,
        [areaKey]: {
          ...previewConfig.areas[areaKey],
          fields: updatedFields,
        },
      },
    };
    setPreviewConfig(newConfig);
    // 实时触发外部配置变更，实现即时预览
    onPreviewConfigChange?.(newConfig);
  };

  // 应用配置
  const applyConfig = () => {
    onConfigChangeAction(previewConfig);
  };

  // 重置配置
  const resetConfig = () => {
    setPreviewConfig(defaultTaskCardConfig);
    // 实时触发外部配置变更，实现即时预览
    onPreviewConfigChange?.(defaultTaskCardConfig);
  };

  // 创建预览任务数据
  const mockTask: Task =
    previewTask ||
    ({
      id: 'preview',
      taskNumber: 'T2024001',
      projectName: '示例项目',
      constructionSite: '示例工地',
      customerName: '示例客户',
      requiredVolume: 100,
      completedVolume: 65,
      dispatchStatus: 'InProgress',
      strength: 'C30',
      contactPhone: '138****8888',
      scheduledTime: '09:00',
      createdAt: '2024-01-01',
      dispatchFrequencyMinutes: 30,
      nextScheduledDispatchTime: new Date(Date.now() + 10 * 60 * 1000).toISOString(),
      isDueForDispatch: true,
      productionLineCount: 3,
    } as Task);

  return (
    <Dialog open={open} onOpenChange={onOpenChangeAction}>
      <DialogContent className='max-w-7xl max-h-[90vh] overflow-y-auto'>
        <DialogHeader>
          <DialogTitle className='flex items-center gap-2'>
            <Settings className='w-5 h-5' />
            任务卡片配置
          </DialogTitle>
        </DialogHeader>

        <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
          {/* 配置区域 */}
          <div className='space-y-6'>
            <Tabs defaultValue='style' className='w-full'>
              <TabsList className='grid w-full grid-cols-3'>
                <TabsTrigger value='style' className='flex items-center gap-1'>
                  <Palette className='w-4 h-4' />
                  样式
                </TabsTrigger>
                <TabsTrigger value='layout' className='flex items-center gap-1'>
                  <Layout className='w-4 h-4' />
                  布局
                </TabsTrigger>
                <TabsTrigger value='fields' className='flex items-center gap-1'>
                  <Type className='w-4 h-4' />
                  字段
                </TabsTrigger>
              </TabsList>

              {/* 样式配置 */}
              <TabsContent value='style' className='space-y-4'>
                <Card>
                  <CardContent className='p-4 space-y-4'>
                    <h3 className='font-medium flex items-center gap-2'>
                      <Sparkles className='w-4 h-4' />
                      卡片样式
                    </h3>

                    <div className='grid grid-cols-2 gap-4'>
                      <div>
                        <Label className='text-sm'>主题</Label>
                        <Select
                          value={previewConfig.style.theme}
                          onValueChange={(theme: any) => updateStyleConfig({ theme })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value='default'>默认</SelectItem>
                            <SelectItem value='modern'>现代</SelectItem>
                            <SelectItem value='glass'>玻璃</SelectItem>
                            <SelectItem value='gradient'>渐变</SelectItem>
                            <SelectItem value='dark'>深色</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label className='text-sm'>圆角</Label>
                        <Select
                          value={previewConfig.style.borderRadius}
                          onValueChange={(borderRadius: any) => updateStyleConfig({ borderRadius })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value='none'>无</SelectItem>
                            <SelectItem value='sm'>小</SelectItem>
                            <SelectItem value='md'>中</SelectItem>
                            <SelectItem value='lg'>大</SelectItem>
                            <SelectItem value='xl'>超大</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label className='text-sm'>阴影</Label>
                        <Select
                          value={previewConfig.style.shadow}
                          onValueChange={(shadow: any) => updateStyleConfig({ shadow })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value='none'>无</SelectItem>
                            <SelectItem value='sm'>小</SelectItem>
                            <SelectItem value='md'>中</SelectItem>
                            <SelectItem value='lg'>大</SelectItem>
                            <SelectItem value='xl'>超大</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label className='text-sm'>动画</Label>
                        <Select
                          value={previewConfig.style.animation}
                          onValueChange={(animation: any) => updateStyleConfig({ animation })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value='none'>无</SelectItem>
                            <SelectItem value='subtle'>微妙</SelectItem>
                            <SelectItem value='smooth'>平滑</SelectItem>
                            <SelectItem value='bouncy'>弹性</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label className='text-sm'>间距</Label>
                        <Select
                          value={previewConfig.style.spacing}
                          onValueChange={(spacing: any) => updateStyleConfig({ spacing })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value='tight'>紧凑</SelectItem>
                            <SelectItem value='normal'>正常</SelectItem>
                            <SelectItem value='loose'>宽松</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label className='text-sm'>内边距</Label>
                        <Select
                          value={previewConfig.style.padding}
                          onValueChange={(padding: any) => updateStyleConfig({ padding })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value='none'>无</SelectItem>
                            <SelectItem value='sm'>小</SelectItem>
                            <SelectItem value='md'>中</SelectItem>
                            <SelectItem value='lg'>大</SelectItem>
                            <SelectItem value='xl'>特大</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label className='text-sm'>卡片密度</Label>
                        <Select
                          value={previewConfig.style.density}
                          onValueChange={(density: any) => updateStyleConfig({ density })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value='compact'>紧凑</SelectItem>
                            <SelectItem value='normal'>正常</SelectItem>
                            <SelectItem value='loose'>宽松</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* 布局配置 */}
              <TabsContent value='layout' className='space-y-4'>
                <Card>
                  <CardContent className='p-4 space-y-4'>
                    <h3 className='font-medium flex items-center gap-2'>
                      <Grid3X3 className='w-4 h-4' />
                      区域布局
                    </h3>

                    <div className='space-y-4'>
                      {/* 顶部区域 */}
                      <div className='flex items-center justify-between p-2 border rounded'>
                        <Label>顶部区域</Label>
                        <Switch
                          checked={previewConfig.areas.top.visible}
                          onCheckedChange={visible => updateAreaConfig('top', { visible })}
                        />
                      </div>

                      {/* 车辆区域 - 不可隐藏 */}
                      <div className='flex items-center justify-between p-2 border rounded bg-muted/30'>
                        <Label>车辆区域</Label>
                        <Badge variant='secondary'>不可隐藏</Badge>
                      </div>

                      {/* 内容区域 */}
                      <div className='space-y-2'>
                        <div className='flex items-center justify-between p-2 border rounded'>
                          <Label>内容区域</Label>
                          <Switch
                            checked={previewConfig.areas.content.visible}
                            onCheckedChange={visible => updateAreaConfig('content', { visible })}
                          />
                        </div>
                        {previewConfig.areas.content.visible && (
                          <div className='ml-4'>
                            <Label className='text-sm'>布局</Label>
                            <Select
                              value={previewConfig.areas.content.layout}
                              onValueChange={(layout: any) =>
                                updateAreaConfig('content', { layout })
                              }
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value='single'>单列</SelectItem>
                                <SelectItem value='double'>双列</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        )}
                      </div>

                      {/* 底部区域 */}
                      <div className='space-y-2'>
                        <div className='flex items-center justify-between p-2 border rounded'>
                          <Label>底部区域</Label>
                          <Switch
                            checked={previewConfig.areas.bottom.visible}
                            onCheckedChange={visible => updateAreaConfig('bottom', { visible })}
                          />
                        </div>
                        {previewConfig.areas.bottom.visible && (
                          <div className='ml-4'>
                            <Label className='text-sm'>布局</Label>
                            <Select
                              value={previewConfig.areas.bottom.layout}
                              onValueChange={(layout: any) =>
                                updateAreaConfig('bottom', { layout })
                              }
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value='single'>单列</SelectItem>
                                <SelectItem value='double'>双列</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* 字段配置 */}
              <TabsContent value='fields' className='space-y-4 max-h-[500px] overflow-y-auto'>
                <div className='space-y-4'>
                  {/* 表头 */}
                  <div className='grid grid-cols-6 gap-2 text-xs font-medium text-muted-foreground p-2 border-b'>
                    <div>字段</div>
                    <div className='text-center'>显示</div>
                    <div className='text-center'>大小</div>
                    <div className='text-center'>粗细</div>
                    <div className='text-center'>颜色</div>
                    <div className='text-center'>对齐</div>
                  </div>

                  {/* 顶部区域字段 */}
                  {previewConfig.areas.top.visible && (
                    <FieldConfigSection
                      title='顶部区域'
                      fields={previewConfig.areas.top.fields}
                      onFieldChange={(fieldKey, field) => updateFieldConfig('top', fieldKey, field)}
                      disabledFields={['progressRing']} // 进度环不可隐藏
                    />
                  )}

                  {/* 车辆区域字段 */}
                  <FieldConfigSection
                    title='车辆区域'
                    fields={previewConfig.areas.vehicle.fields}
                    onFieldChange={(fieldKey, field) =>
                      updateFieldConfig('vehicle', fieldKey, field)
                    }
                    disabledFields={['vehicleCards']} // 车辆卡片不可隐藏
                  />

                  {/* 内容区域字段 */}
                  {previewConfig.areas.content.visible && (
                    <DraggableFieldConfigSection
                      title='内容区域'
                      fields={previewConfig.areas.content.fields}
                      onFieldChange={(fieldKey, field) =>
                        updateFieldConfig('content', fieldKey, field)
                      }
                      onFieldOrderChange={newOrder => updateFieldOrder('content', newOrder)}
                    />
                  )}

                  {/* 底部区域字段 */}
                  {previewConfig.areas.bottom.visible && (
                    <DraggableFieldConfigSection
                      title='底部区域'
                      fields={previewConfig.areas.bottom.fields}
                      onFieldChange={(fieldKey, field) =>
                        updateFieldConfig('bottom', fieldKey, field)
                      }
                      onFieldOrderChange={newOrder => updateFieldOrder('bottom', newOrder)}
                    />
                  )}
                </div>
              </TabsContent>
            </Tabs>
          </div>

          {/* 实时预览 */}
          <div className='space-y-4'>
            <h3 className='font-medium flex items-center gap-2'>
              <Eye className='w-4 h-4' />
              实时预览
            </h3>
            <div className='border rounded-lg p-6 bg-muted/30 flex justify-center min-h-[320px] items-start'>
              <div className='transform scale-90 origin-top'>
                <TaskRowHighlightProvider>
                  <ConfigurableTaskCard
                    task={mockTask}
                    vehicles={previewVehicles}
                    config={previewConfig}
                  />
                </TaskRowHighlightProvider>
              </div>
            </div>
          </div>
        </div>

        {/* 底部按钮 */}
        <div className='flex justify-between pt-4 border-t'>
          <Button variant='outline' onClick={resetConfig} className='flex items-center gap-2'>
            <RotateCcw className='w-4 h-4' />
            重置
          </Button>
          <div className='flex gap-2'>
            <Button variant='outline' onClick={() => onOpenChangeAction(false)}>
              取消
            </Button>
            <Button
              onClick={() => {
                applyConfig();
                onOpenChangeAction(false);
              }}
            >
              应用配置
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
